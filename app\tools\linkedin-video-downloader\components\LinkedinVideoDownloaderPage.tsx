"use client";

import { useState } from "react";
import TextGradient from "@/components/ui/text-gradient";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Ripple } from "@/components/magicui/ripple";
import FAQSection from "@/app/components/FAQSection";
import HowToStepsSection from "@/app/tools/shared/HowToStepsSection";
import William<PERSON>taSection from "@/app/tools/shared/WilliamCtaSection";
import { detectPlatform, VideoOption, PLATFORMS } from "@/lib/platform-config";
import React from "react";
import Link from "next/link";
import { downloadVideoAction } from "@/app/actions/video-download";
import { useGtmTrack } from "@/lib/tracking";
import Meta from "@/app/components/meta";

const RippleBackground = () => {
  return (
    <div className="absolute inset-0 overflow-hidden">
      <div className="absolute inset-0 [mask-image:radial-gradient(circle_at_center,white,transparent_80%)]">
        <Ripple
          mainCircleSize={400}
          mainCircleOpacity={0.25}
          numCircles={8}
          className="opacity-70 [--ripple-bg:hsl(var(--primary)_/_0.15)]"
          style={
            {
              "--foreground": "var(--primary)",
            } as React.CSSProperties
          }
        />
      </div>
    </div>
  );
};

export default function LinkedinVideoDownloaderPage() {
  const [url, setUrl] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [videoOptions, setVideoOptions] = useState<VideoOption[]>([]);
  const [selectedVideoUrl, setSelectedVideoUrl] = useState("");
  const [title, setTitle] = useState<string>("");

  const { downloadVideoTrack } = useGtmTrack();
  const currentPlatform = detectPlatform(url);
  const platformConfig = currentPlatform ? PLATFORMS[currentPlatform] : null;
  const isLinkedinUrl = currentPlatform === "Linkedin";

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = e.target.value;
    setUrl(newUrl);
    setError("");
    setVideoOptions([]);
    setSelectedVideoUrl("");
    setTitle("");
  };

  const handleGetVideo = async () => {
    if (!url) {
      setError("Please enter a LinkedIn video URL");
      return;
    }

    if (!isLinkedinUrl) {
      setError("Please enter a valid LinkedIn video URL");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const result = await downloadVideoAction(url);

      if (!result.success) {
        setError(result.error || "An error occurred while getting the video");
        return;
      }

      if (result.data) {
        setVideoOptions(result.data.videoOptions);
        setTitle(result.data.title || "LinkedIn Video");

        if (result.data.videoOptions.length === 1) {
          setSelectedVideoUrl(result.data.videoOptions[0].url);
        }
        downloadVideoTrack("linkedin");
      }
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "An error occurred while getting the video"
      );
    } finally {
      setLoading(false);
    }
  };

  const openDownloadWindow = () => {
    window.open(selectedVideoUrl, "_blank");
  };

  const handleReset = () => {
    setUrl("");
    setVideoOptions([]);
    setSelectedVideoUrl("");
    setTitle("");
    setError("");
  };

  const hasVideoOptions = videoOptions.length > 0;

  const faqItems = [
    {
      question: "Is this tool free?",
      answer:
        "Yes, this LinkedIn video downloader tool is completely free to use—no hidden fees or sign-ups required.",
    },
    {
      question: "Does Inloop offer more free tools?",
      answer: (
        <span>
          Absolutely! Inloop provides additional free LinkedIn Tools designed to
          help you increase your engagement and grow your presence on the
          platform. Check our AI Agent William at{" "}
          <Link
            href="https://tryinloop.com/"
            className="text-primary hover:underline"
          >
            tryinloop.com
          </Link>
          .
        </span>
      ),
    },
    {
      question: "What else can you do with a LinkedIn Video?",
      answer:
        "LinkedIn videos are powerful for engagement, but repurposing them can expand your reach even further. With Inloop, you can also use the same tool to create LinkedIn post drafts directly from the video link. Easily tailor the tone, insert a compelling hook, and add a strong call-to-action to align with your messaging.",
    },
  ];

  return (
    <main className="relative overflow-hidden min-h-screen bg-background">
      {/* Background Effects */}
      <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary/[0.03] via-transparent to-transparent pointer-events-none"></div>
      <div className="sm:block hidden absolute top-20 left-[30%] opacity-[0.08] transform scale-150 gradient-blob"></div>

      {/* Section 1: Hero Section */}
      <div className="relative pt-32 pb-20">
        <div className="max-w-6xl mx-auto px-4">
          {/* Header Section */}
          <div className="text-center mb-16">
            <h1 className="mb-6 text-4xl sm:text-6xl leading-tight font-medium text-foreground">
              Download <TextGradient txt="Linkedin Video" /> for Free
            </h1>
            <h2 className="text-xl sm:text-2xl font-normal text-muted-foreground max-w-2xl mx-auto leading-relaxed mb-8">
              Download Video Clips from Linkedin & Repurpose for Later
            </h2>
          </div>

          {/* Main Form Card */}
          <div className="relative max-w-4xl mx-auto">
            <div className="relative p-8 sm:p-12 rounded-3xl shadow shadow-primary/20 overflow-hidden bg-gradient-to-br from-background/60 to-background/40 backdrop-blur-md border border-primary/20">
              <RippleBackground />

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full blur-xl"></div>
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-primary/5 rounded-full blur-2xl"></div>

              <div className="relative z-10">
                {!hasVideoOptions ? (
                  <div className="space-y-8">
                    {/* URL Input Section */}
                    <div className="max-w-2xl mx-auto space-y-6">
                      <div>
                        <label className="block text-lg font-medium mb-4 text-foreground text-center">
                          Enter Linkedin Video URL
                        </label>
                      </div>

                      <div className="flex gap-3 items-center">
                        <input
                          type="text"
                          className="flex-1 px-4 py-3 rounded-lg border border-border bg-background/80 backdrop-blur-sm text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all h-12"
                          placeholder="https://www.linkedin.com/posts/..."
                          value={url}
                          onChange={handleInputChange}
                        />
                        <Button
                          onClick={handleGetVideo}
                          disabled={loading || !url}
                          className={cn(
                            "px-6 py-3 whitespace-nowrap cursor-pointer h-12",
                            "bg-primary text-primary-foreground hover:bg-primary/90"
                          )}
                        >
                          {loading ? (
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin"></div>
                              Processing...
                            </div>
                          ) : (
                            "Download Video"
                          )}
                        </Button>
                      </div>

                      {url && (
                        <div className="flex items-center gap-2 text-sm justify-center">
                          {isLinkedinUrl ? (
                            <div className="flex items-center gap-2 text-primary">
                              <div className="w-2 h-2 bg-primary rounded-full"></div>
                              LinkedIn URL detected
                            </div>
                          ) : (
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <div className="w-2 h-2 bg-muted-foreground rounded-full"></div>
                              Please enter a valid LinkedIn URL
                            </div>
                          )}
                        </div>
                      )}

                      {error && (
                        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 text-destructive">⚠️</div>
                            <span className="text-destructive text-sm font-medium">
                              {error}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-8">
                    {/* Video Ready Section */}
                    <div className="text-center">
                      <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-primary/20 flex items-center justify-center">
                        <svg
                          className="w-6 h-6 text-primary"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      </div>
                      <h3 className="text-xl font-semibold text-foreground mb-4">
                        Video Ready for Download!
                      </h3>

                      {title && (
                        <p className="text-foreground text-sm font-medium bg-muted/30 rounded px-4 py-2 mt-2 inline-block max-w-md">
                          {title}
                        </p>
                      )}
                    </div>

                    <div className="max-w-md mx-auto space-y-6">
                      <div className="flex flex-col gap-3">
                        <Button
                          onClick={openDownloadWindow}
                          disabled={!selectedVideoUrl}
                          className={cn(
                            "w-full bg-primary hover:bg-primary/90 text-primary-foreground cursor-pointer",
                            "disabled:opacity-50 disabled:cursor-not-allowed"
                          )}
                        >
                          <div className="flex items-center justify-center gap-2">
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                              />
                            </svg>
                            Download Video
                          </div>
                        </Button>

                        <Button
                          onClick={handleReset}
                          disabled={!selectedVideoUrl}
                          variant="outline"
                          className="w-full backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                        >
                          Download Another
                        </Button>
                      </div>

                      <p className="text-xs text-muted-foreground text-center">
                        Click &apos;Download Video&apos; to open. Right-click to
                        save on desktop, or press and hold on mobile.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section 2: How to Download */}
      <HowToStepsSection
        heading="How to download a Linkedin video?"
        subheading="Follow these simple steps to download any LinkedIn video"
        iconText="Step by Step Guide"
        steps={[
          {
            number: 1,
            title: "Copy the video URL",
            description:
              "Navigate to the LinkedIn post with the video you want to download and copy the URL from your browser's address bar.",
          },
          {
            number: 2,
            title: "Paste the URL in text box on Linkedin Video Downloader",
            description:
              "Paste the copied URL into the input field above and our system will automatically detect it's a LinkedIn video.",
          },
          {
            number: 3,
            title: "Click on Download Video button",
            description:
              "Hit the Download Video button and your video will be processed and downloaded to your device automatically.",
          },
        ]}
      />

      {/* Section 3: William CTA */}
      <WilliamCtaSection
        badge="AI Agent for LinkedIn"
        title="Inloop AI Agent William for Linkedin"
        description="William is an AI Agent for Linkedin. He automates Linkedin Content creation using Trending Topics, Industry News, and Viral Templates. Use him to build a Personal Brand or turn your team into Linkedin Micro Influencers."
        ctaText="Try William for Free"
        ctaUrl="https://william.tryinloop.com/login"
      />

      {/* Section 4: LinkedIn Video Tool Benefits */}
      <section className="py-16 w-full max-w-6xl mx-auto px-4">
        <Meta
          heading="LinkedIn Video Downloader Tool"
          subheading="Fast, reliable, and completely free LinkedIn video downloading"
          iconText="Free Video Tool"
          className="mb-12"
        />

        <div className="prose prose-lg max-w-4xl mx-auto text-muted-foreground">
          <p className="text-xl leading-relaxed mb-8">
            Looking for a fast and reliable way to download LinkedIn videos?
            Inloop&apos;s free LinkedIn video downloader tool lets you save any
            LinkedIn video in just a few clicks—no registration required, no
            hidden charges. It&apos;s simple, efficient, and built for
            professionals who value their time.
          </p>

          <h3 className="text-2xl font-bold text-foreground mb-6">
            Why LinkedIn Videos Are Worth Your Attention
          </h3>
          <p className="mb-6">
            Still wondering if video content is really worth it on LinkedIn?
            Here&apos;s what&apos;s happening behind the scenes:
          </p>

          <div className="bg-primary/5 border border-primary/20 rounded-lg p-6 mb-8">
            <ul className="space-y-3 text-foreground">
              <li className="flex items-start gap-3">
                <span className="text-primary font-bold">•</span>
                They also lead to 5 times more conversations, which means more
                meaningful interactions.
              </li>
              <li className="flex items-start gap-3">
                <span className="text-primary font-bold">•</span>
                85% of B2B marketers say video helps them bring in new leads and
                78% say it&apos;s helped boost their brand awareness.
              </li>
              <li className="flex items-start gap-3">
                <span className="text-primary font-bold">•</span>
                Videos get 3 times more engagement than text-only posts.
              </li>
            </ul>
          </div>

          <p className="mb-8">
            The bottom line? Video isn&apos;t just a nice-to-have
            anymore—it&apos;s what gets noticed. Use the Inloop LinkedIn Video
            Downloader Tool to save the videos that catch your eye, learn from
            them, or repurpose them for inspiration. It&apos;s free, fast, and
            doesn&apos;t require any sign-up. Just copy, paste, download—and
            you&apos;re good to go.
          </p>

          <h3 className="text-2xl font-bold text-foreground mb-6">
            Why You Should Use Video on LinkedIn
          </h3>
          <p className="mb-4">
            If you&apos;re still on the fence, consider this:
          </p>

          <div className="grid md:grid-cols-3 gap-6 my-8">
            {[
              {
                title: "Higher Engagement",
                description:
                  "Video posts generate 5x more interactions than text",
              },
              {
                title: "Stronger Brand Trust",
                description: "Consistent video content builds credibility",
              },
              {
                title: "Increased Visibility",
                description: "Video content stands out and commands attention",
              },
            ].map((benefit, index) => (
              <div
                key={index}
                className="bg-card/60 border border-primary/20 rounded-lg p-6 backdrop-blur-sm"
              >
                <h4 className="font-semibold text-foreground mb-3">
                  {benefit.title}
                </h4>
                <p className="text-sm text-muted-foreground">
                  {benefit.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Section 5: FAQ */}
      <FAQSection
        heading="Linkedin Video Downloader Tool FAQ"
        subheading="Common questions about our free LinkedIn video downloader"
        iconText="Frequently Asked"
        faqItems={faqItems}
        className="px-4"
      />
    </main>
  );
}
