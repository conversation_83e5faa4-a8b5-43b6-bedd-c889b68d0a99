import { Button } from "@/components/ui/button";
import TextGradient from "@/components/ui/text-gradient";
import { Ripple } from "@/components/magicui/ripple";
import Link from "next/link";
import { Bot } from "lucide-react";

const RippleBackground = () => {
  return (
    <div className="absolute inset-0 overflow-hidden">
      <div className="absolute inset-0 [mask-image:radial-gradient(circle_at_center,white,transparent_80%)]">
        <Ripple
          mainCircleSize={400}
          mainCircleOpacity={0.25}
          numCircles={8}
          className="opacity-70 [--ripple-bg:hsl(var(--primary)_/_0.15)]"
          style={{ "--foreground": "var(--primary)" } as React.CSSProperties}
        />
      </div>
    </div>
  );
};

type WilliamCtaSectionProps = {
  badge: string;
  title: string;
  description: string;
  ctaText: string;
  ctaUrl: string;
  className?: string;
};

export default function WilliamCtaSection({
  badge,
  title,
  description,
  ctaText,
  ctaUrl,
  className = "",
}: WilliamCtaSectionProps) {
  // Split title to highlight "<PERSON>" with gradient
  const titleParts = title.split("William");
  
  return (
    <section className={`py-24 overflow-hidden relative ${className}`}>
      <div className="max-w-6xl mx-auto px-4 relative">
        <div className="relative p-12 sm:p-20 rounded-3xl shadow shadow-primary/20 overflow-hidden bg-gradient-to-br from-background/60 to-background/40 backdrop-blur-md border border-primary/20">
          <RippleBackground />
          <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full blur-xl"></div>
          <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-primary/5 rounded-full blur-2xl"></div>

          <div className="relative z-10 text-center">
            <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-8">
              <Bot className="w-4 h-4" />
              {badge}
            </div>

            <h2 className="mb-6 text-3xl sm:text-5xl leading-tight font-medium text-foreground">
              {titleParts[0]}
              <TextGradient txt="William" />
              {titleParts[1]}
            </h2>

            <p className="mx-auto mb-12 text-lg md:text-xl lg:text-xl text-muted-foreground/90 leading-relaxed max-w-3xl">
              {description}
            </p>

            <div className="flex justify-center">
              <Button asChild className="rounded-full cursor-pointer">
                <Link
                  href={ctaUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {ctaText}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
