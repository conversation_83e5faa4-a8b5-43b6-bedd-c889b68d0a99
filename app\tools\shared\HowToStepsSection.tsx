import Meta from "@/app/components/meta";

type Step = {
  number: number;
  title: string;
  description: string;
};

type HowToStepsSectionProps = {
  heading: string;
  subheading: string;
  iconText: string;
  steps: Step[];
  className?: string;
};

export default function HowToStepsSection({
  heading,
  subheading,
  iconText,
  steps,
  className = "",
}: HowToStepsSectionProps) {
  return (
    <section className={`py-16 w-full max-w-6xl mx-auto px-4 ${className}`}>
      <Meta
        heading={heading}
        subheading={subheading}
        iconText={iconText}
        className="mb-12"
      />

      <div className="grid md:grid-cols-3 gap-8">
        {steps.map((step) => (
          <div key={step.number} className="text-center">
            <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-primary/20 flex items-center justify-center">
              <span className="text-2xl font-bold text-primary">{step.number}</span>
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-4">
              {step.title}
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              {step.description}
            </p>
          </div>
        ))}
      </div>
    </section>
  );
}
