import Meta from "@/app/components/meta";
import { 
  Smartphone, 
  Zap, 
  Target, 
  Monitor, 
  RefreshCw, 
  Video,
  LucideIcon 
} from "lucide-react";

// Icon mapping for feature icons
const iconMap: Record<string, LucideIcon> = {
  Smartphone,
  Zap,
  Target,
  Monitor,
  RefreshCw,
  Video,
};

type Feature = {
  title: string;
  description: string;
  icon: string;
};

type ToolBenefitsSectionProps = {
  heading: string;
  subheading: string;
  iconText: string;
  features: Feature[];
  footer?: string;
  className?: string;
};

export default function ToolBenefitsSection({
  heading,
  subheading,
  iconText,
  features,
  footer,
  className = "",
}: ToolBenefitsSectionProps) {
  return (
    <section className={`py-16 w-full max-w-6xl mx-auto px-4 ${className}`}>
      <Meta
        heading={heading}
        subheading={subheading}
        iconText={iconText}
        className="mb-12"
      />

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {features.map((feature, index) => {
          const IconComponent = iconMap[feature.icon];
          return (
            <div
              key={index}
              className="bg-card/60 border border-primary/20 rounded-lg p-6 backdrop-blur-sm"
            >
              <div className="w-12 h-12 mb-4 rounded-full bg-primary/20 flex items-center justify-center">
                {IconComponent && (
                  <IconComponent className="w-6 h-6 text-primary" />
                )}
              </div>
              <h4 className="font-semibold text-foreground mb-3">
                {feature.title}
              </h4>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {feature.description}
              </p>
            </div>
          );
        })}
      </div>

      {footer && (
        <div className="text-center mt-12">
          <p className="text-lg text-muted-foreground">
            {footer}
          </p>
        </div>
      )}
    </section>
  );
}
